import { defineAsyncComponent, markRaw } from 'vue';
import { getDictionary } from '@/applications/unified_portal/api';
import {
	actions as mixinActions,
	state as mixinState,
	getters as mixinGettgers,
} from '@/utils/tab.mixin';
const dictionary = localStorage.getItem(`kade_cache_dictionary`);
const state = {
	...mixinState,
	dictionary: dictionary ? JSON.parse(dictionary) : [],
	componentMap: {
		financeHome: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "shopmanager" */ '../../views/home'))),
		//个人账户管理
		accountManagerInfo: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "shopmanager" */ '../../views/accountManager/account'))),
		changeCreditInfo: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "shopmanager" */ '../../views/accountManager/ChangeCreditInfo'))),
		correctError: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "shopmanager" */ '../../views/accountManager/CorrectError'))),
		personRefund: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "shopmanager" */ '../../views/accountManager/PersonRefund'))),
		//商户管理
		shopInfo: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "shopmanager" */ '../../views/shopManager/shopInfo'))),
		ShopEdit: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "shopmanager" */ '../../views/shopManager/shopInfo/edit'))),
		shopSettlement: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "shopmanager" */ '../../views/shopManager/settlement'))),
		shopEquipment: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "shopmanager" */ '../../views/shopManager/equipment'))),
		//现金管理
		cashGrant: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "cashManager" */ '../../views/cashManager/cashGrant/index.vue'))),
		cashReport: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "cashManager" */ '../../views/cashManager/cashReport/index.vue'))),
		//补助管理
		subsidyGrant: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "grantManager" */ '../../views/grantManager/SubsidyGrant/index.vue'))),
		subsidyType: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "grantManager" */'../../views/grantManager/SubsidyType/index.vue'))),
		subsidyTypeInfo: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "grantManager" */ '../../views/shopManager/shopInfo'))),
		subsidyTypeEdit: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "grantManager" */ '../../views/shopManager/shopInfo/edit'))),
		//次数管理
		frequencyType: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "frequencyManager" */ '../../views/frequencyManager/frequencyType/index.vue'))),
		frequencyGrant: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "frequencyManager" */ '../../views/frequencyManager/frequencyGrant/index.vue'))),
		frequencyReport: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "frequencyManager" */ '../../views/frequencyManager/frequencyReport/index.vue'))),
		//系统管理
		accountStrategy: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "sysManager" */ '../../views/sysManager/accountStrategy/index.vue'))),
		tradeSource: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "sysManager" */ '../../views/sysManager/tradeSource/index.vue'))),
		tradeType: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "sysManager" */ '../../views/sysManager/tradeType/index.vue'))),
		//统计报表
		cashierPayment: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "frequencyManager" */ '../../views/reportManager/cashierPayment/index.vue'))),
		cashRecharge: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "frequencyManager" */ '../../views/reportManager/cashRecharge/index.vue'))),
		depositDetails: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "frequencyManager" */ '../../views/reportManager/depositDetails/index.vue'))),
		merchantSettlement: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "frequencyManager" */ '../../views/reportManager/merchantSettlement/index.vue'))),
		onlinePayment: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "frequencyManager" */ '../../views/reportManager/onlinePayment/index.vue'))),
		onlineRecharge: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "frequencyManager" */ '../../views/reportManager/onlineRecharge/index.vue'))),
		personalAccountBalance: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "frequencyManager" */ '../../views/reportManager/personalAccountBalance/index.vue'))),
		personalSettlement: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "frequencyManager" */ '../../views/reportManager/personalSettlement/index.vue'))),
		personalTrading: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "frequencyManager" */ '../../views/reportManager/personalTrading/index.vue'))),
		productionCost: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "frequencyManager" */ '../../views/reportManager/productionCost/index.vue'))),
		subsidyDetails: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "frequencyManager" */ '../../views/reportManager/subsidyDetails/index.vue'))),
		timesRecharge: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "frequencyManager" */ '../../views/reportManager/timesRecharge/index.vue'))),

		consumeSummary: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "frequencyManager" */ '../../views/reportManager/consumeSummary/index.vue'))),
		cashierDetails: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "frequencyManager" */ '../../views/reportManager/cashierDetails/index.vue'))),

		cashierSummary: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "frequencyManager" */ '../../views/reportManager/cashierSummary/index.vue'))),
		systemSettlement: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "frequencyManager" */ '../../views/reportManager/systemSettlement/index.vue'))),

		rechargeSummary: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "frequencyManager" */ '../../views/reportManager/rechargeSummary/index.vue'))),
		consumptionSummary: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "frequencyManager" */ '../../views/reportManager/consumptionSummary/index.vue'))),
		cardSortSummary: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "frequencyManager" */ '../../views/reportManager/cardSortSummary/index.vue'))),

		
		transactionStatistics: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "frequencyManager" */ '../../views/reportManager/transactionStatistics/index.vue'))),
		//数据文件
		dataFile: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "frequencyManager" */ '../../views/dataFile/index.vue'))),
		//交易管理
		wallet: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "tradeManager" */ '../../views/tradeManager/wallet/index.vue'))),
		unifiedChargeType: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "tradeManager" */ '../../views/tradeManager/unifiedChargeType/index.vue'))),
		unifiedCharge: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "tradeManager" */ '../../views/tradeManager/unifiedCharge/index.vue'))),
	},
	customMenus: []
};
const mutations = {
	updateState(state, { key, payload }) {
		state[key] = payload;
	},
};
const actions = {
	...mixinActions,
	async loadDictionary({ commit }) {
		const { data } = await getDictionary();
		localStorage.setItem(`kade_cache_dictionary`, JSON.stringify(data));
		commit('updateState', { key: 'dictionary', payload: data });
	},
};

const getters = {
	...mixinGettgers,
};

export default {
	namespaced: true,
	state,
	mutations,
	actions,
	getters,
}
